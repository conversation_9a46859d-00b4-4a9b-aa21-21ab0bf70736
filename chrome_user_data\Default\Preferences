{"NewTabPage": {"PrevNavigationTime": "*****************"}, "accessibility": {"captions": {"headless_caption_enabled": false, "live_caption_language": "cmn-Hans-CN"}}, "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": false}, "autocomplete": {"retention_policy_last_version": 138}, "autofill": {"last_version_deduped": 138, "metadata_upload_events": {"3AB": 1}, "secondary_form_signature_upload_events": {"0D6": 1}, "upload_encoding_seed": "ABE2DA78278B5E5127E6D2868C080249", "upload_events_last_reset_timestamp": "*****************"}, "bookmark": {"storage_computation_last_update": "*****************"}, "browser": {"check_default_browser": false, "window_placement": {"bottom": 991, "left": 0, "maximized": false, "right": 1636, "top": 0, "work_area_bottom": 600, "work_area_left": 0, "work_area_right": 800, "work_area_top": 0}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 17230, "default_search_provider": {"guid": ""}, "devtools": {"last_open_timestamp": "**************", "preferences": {"closeable-tabs": "{\"security\":true,\"freestyler\":true,\"chrome-recorder\":true}", "console.sidebar-selected-filter": "\"message\"", "console.sidebar.width": "{\"vertical\":{\"size\":0,\"showMode\":\"OnlyMain\"}}", "currentDockState": "\"right\"", "disable-locale-info-bar": "true", "elements.styles.sidebar.width": "{\"vertical\":{\"size\":0,\"showMode\":\"OnlyMain\"}}", "inspector-view.split-view-state": "{\"vertical\":{\"size\":455}}", "inspector.drawer-split-view-state": "{\"horizontal\":{\"size\":0,\"showMode\":\"OnlyMain\"}}", "inspectorVersion": "38", "panel-selected-tab": "\"console\"", "releaseNoteVersionSeen": "79", "styles-pane-sidebar-tab-order": "{\"styles\":10,\"computed\":20}"}, "synced_preferences_sync_disabled": {"adorner-settings": "[{\"adorner\":\"grid\",\"isEnabled\":true},{\"adorner\":\"subgrid\",\"isEnabled\":true},{\"adorner\":\"flex\",\"isEnabled\":true},{\"adorner\":\"ad\",\"isEnabled\":true},{\"adorner\":\"scroll-snap\",\"isEnabled\":true},{\"adorner\":\"container\",\"isEnabled\":true},{\"adorner\":\"slot\",\"isEnabled\":true},{\"adorner\":\"top-layer\",\"isEnabled\":true},{\"adorner\":\"reveal\",\"isEnabled\":true},{\"adorner\":\"media\",\"isEnabled\":false},{\"adorner\":\"scroll\",\"isEnabled\":true}]", "disable-self-xss-warning": "true", "language": "\"zh\"", "syncedInspectorVersion": "38"}}, "distribution": {"import_bookmarks": false, "import_history": false, "import_search_engine": false, "make_chrome_default_for_user": false, "skip_first_run_ui": true}, "dns_prefetching": {"enabled": false}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "enterprise_profile_guid": "9dcc79f8-099a-4193-ba17-0a235a53363d", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}}, "gaia_cookie": {"changed_time": **********.023381, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_data": "[\"gaia.l.a.r\",[]]"}, "gcm": {"product_category_for_subtypes": "com.chrome.windows"}, "google": {"services": {"signin_scoped_device_id": "82cfc134-526f-4d7d-8e8a-22791463b214"}}, "https_upgrade_navigations": {"2025-07-26": 80, "2025-07-27": 80, "2025-07-28": 80, "2025-07-29": 20, "2025-07-31": 10, "2025-08-02": 20}, "in_product_help": {"new_badge": {"Compose": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeProactiveNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "LensOverlay": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "PasswordManualFallbackAvailable": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}}, "recent_session_enabled_time": "*****************", "recent_session_start_times": ["*****************", "*****************", "13398488231783146", "13398405120981231", "13398268199561332", "13398188611988811", "13398101184624276", "13398058903005481", "*****************"], "session_last_active_time": "*****************", "session_start_time": "*****************"}, "intl": {"accept_languages": "zh-CN,zh", "selected_languages": "zh-CN,zh"}, "invalidation": {"per_sender_topics_to_handler": {"1013309121859": {}}}, "language_model_counters": {"zh-CN": 63}, "media": {"engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "SXDBqLU0cwe0yTrIPEuvlkQAXxJYpdApLAaxKB+oz5rGkFVbqpWetULfvuke5uM0SZvnlvX8ssdKIKCYVetUeQ=="}, "migrated_user_scripts_toggle": true, "ntp": {"num_personal_suggestions": 6}, "omnibox": {"shown_count_history_scope_promo": 3}, "optimization_guide": {"predictionmodelfetcher": {"last_fetch_attempt": "13398583906908065", "last_fetch_success": "13398488293140705"}, "previous_optimization_types_with_filter": {"AMERICAN_EXPRESS_CREDIT_CARD_FLIGHT_BENEFITS": true, "AMERICAN_EXPRESS_CREDIT_CARD_SUBSCRIPTION_BENEFITS": true, "AUTOFILL_ABLATION_SITES_LIST1": true, "AUTOFILL_ABLATION_SITES_LIST2": true, "AUTOFILL_ABLATION_SITES_LIST3": true, "AUTOFILL_ABLATION_SITES_LIST4": true, "AUTOFILL_ABLATION_SITES_LIST5": true, "AUTOFILL_PREDICTION_IMPROVEMENTS_ALLOWLIST": true, "BMO_CREDIT_CARD_AIR_MILES_PARTNER_BENEFITS": true, "BMO_CREDIT_CARD_ALCOHOL_STORE_BENEFITS": true, "BMO_CREDIT_CARD_DINING_BENEFITS": true, "BMO_CREDIT_CARD_DRUGSTORE_BENEFITS": true, "BMO_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "BMO_CREDIT_CARD_GROCERY_BENEFITS": true, "BMO_CREDIT_CARD_OFFICE_SUPPLY_BENEFITS": true, "BMO_CREDIT_CARD_RECURRING_BILL_BENEFITS": true, "BMO_CREDIT_CARD_TRANSIT_BENEFITS": true, "BMO_CREDIT_CARD_TRAVEL_BENEFITS": true, "BMO_CREDIT_CARD_WHOLESALE_CLUB_BENEFITS": true, "BUY_NOW_PAY_LATER_ALLOWLIST_AFFIRM": true, "BUY_NOW_PAY_LATER_ALLOWLIST_ZIP": true, "CAPITAL_ONE_CREDIT_CARD_BENEFITS_BLOCKED": true, "CAPITAL_ONE_CREDIT_CARD_DINING_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_GROCERY_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_STREAMING_BENEFITS": true, "DIGITAL_CREDENTIALS_LOW_FRICTION": true, "EWALLET_MERCHANT_ALLOWLIST": true, "GLIC_ACTION_PAGE_BLOCK": true, "HISTORY_CLUSTERS": true, "HISTORY_EMBEDDINGS": true, "IBAN_AUTOFILL_BLOCKED": true, "PIX_MERCHANT_ORIGINS_ALLOWLIST": true, "PIX_PAYMENT_MERCHANT_ALLOWLIST": true, "SHARED_CREDIT_CARD_DINING_BENEFITS": true, "SHARED_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "SHARED_CREDIT_CARD_FLIGHT_BENEFITS": true, "SHARED_CREDIT_CARD_GROCERY_BENEFITS": true, "SHARED_CREDIT_CARD_STREAMING_BENEFITS": true, "SHARED_CREDIT_CARD_SUBSCRIPTION_BENEFITS": true, "SHOPPING_PAGE_PREDICTOR": true, "TEXT_CLASSIFIER_ENTITY_DETECTION": true, "VCN_MERCHANT_OPT_OUT_DISCOVER": true, "VCN_MERCHANT_OPT_OUT_MASTERCARD": true, "VCN_MERCHANT_OPT_OUT_VISA": true}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PAGE_ENTITIES": true, "PRICE_TRACKING": true, "SAVED_TAB_GROUP": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"account_store_migrated_to_os_crypt_async": true, "autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": true, "biometric_authentication_filling_promo_counter": 3, "profile_store_date_last_used_for_filling": "*****************", "profile_store_migrated_to_os_crypt_async": true}, "privacy_sandbox": {"fake_notice": {"prompt_shown_time": "*****************", "prompt_shown_time_sync": "*****************"}, "first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 26, "background_password_check": {"check_fri_weight": 9, "check_interval": "*************", "check_mon_weight": 6, "check_sat_weight": 6, "check_sun_weight": 6, "check_thu_weight": 9, "check_tue_weight": 9, "check_wed_weight": 9, "next_check_time": "*****************"}, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {"https://[*.]taobao.com,https://[*.]taobao.com": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": 1}}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"https://[*.]taobao.com,*": {"last_modified": "*****************", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": false}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "initialized_translations": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"https://jingya-x.taobao.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 280}}, "https://jingya.taobao.com:443,*": {"expiration": "13406359721606803", "last_modified": "13398583721606805", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 38}}, "https://login.m.taobao.com:443,*": {"expiration": "13405761915618210", "last_modified": "13397985915618214", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 2}}, "https://login.taobao.com:443,*": {"expiration": "13406359693400034", "last_modified": "13398583693400036", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 137}}, "https://myseller.taobao.com:443,*": {"expiration": "13405880047786201", "last_modified": "13398104047786204", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 11}}, "https://passport.taobao.com:443,*": {"expiration": "13406284183132871", "last_modified": "13398508183132872", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 7}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "ondevice_languages_downloaded": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"chrome://newtab/,*": {"last_modified": "13398583680389242", "setting": {"lastEngagementTime": 1.3398583680389234e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 4.5, "rawScore": 70.43777817595493}}, "https://jingya-x.taobao.com:443,*": {"last_modified": "13398583924912283", "setting": {"lastEngagementTime": 1.3398583924912268e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 15.0, "rawScore": 87.32059303953027}}, "https://jingya.taobao.com:443,*": {"last_modified": "13398583696076064", "setting": {"lastEngagementTime": 1.3398583696076056e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 42.020897744107}}, "https://login.m.taobao.com:443,*": {"last_modified": "13398583665838143", "setting": {"lastEngagementTime": 1.339831280169642e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 2.7}}, "https://login.taobao.com:443,*": {"last_modified": "13398583665838131", "setting": {"lastEngagementTime": 1.339855467717522e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 15.899941517631804}}, "https://myseller.taobao.com:443,*": {"last_modified": "13398583665838156", "setting": {"lastEngagementTime": 1.339840119156438e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 23.8329563543306}}, "https://passport.taobao.com:443,*": {"last_modified": "13398583665838168", "setting": {"lastEngagementTime": 1.3398554624249272e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 9.199318588879018}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "suspicious_notification_ids": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pattern_pairs": {"https://*,*": {"media-stream": {"audio": "<PERSON><PERSON><PERSON>", "video": "<PERSON><PERSON><PERSON>"}}}, "pref_version": 1}, "creation_time": "13397980605263915", "default_content_setting_values": {"geolocation": 2, "media_stream": 2, "notifications": 2}, "default_content_settings": {"geolocation": 1, "mouselock": 1, "notifications": 1, "popups": 0, "ppapi-broker": 1}, "exit_type": "Normal", "family_member_role": "not_in_family", "last_engagement_time": "13398583924912268", "last_time_obsolete_http_credentials_removed": 1753507452.64246, "last_time_password_store_metrics_reported": 1754034568.627454, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_default_content_settings": {"images": 1}, "managed_user_id": "", "name": "您的 Chrome", "password_hash_data_list": [], "password_manager_enabled": false, "safety_hub_menu_notifications": {"extensions": {"isCurrentlyActive": false, "result": {"timestamp": "13398102236008543", "triggeringExtensions": []}}, "notification-permissions": {"isCurrentlyActive": false, "result": {"notificationPermissions": [], "timestamp": "13398102233513008"}}, "passwords": {"isCurrentlyActive": false, "result": {"passwordCheckOrigins": [], "timestamp": "13398102233700531"}}, "safe-browsing": {"firstImpressionTime": "13398102236008560", "impressionCount": 1, "isCurrentlyActive": true, "lastImpressionTime": "13398102236008560", "onlyShowAfterTime": "13398072246189935", "result": {"safeBrowsingStatus": 4, "timestamp": "13398102236008523"}}, "unused-site-permissions": {"isCurrentlyActive": false, "result": {"permissions": [], "timestamp": "13398102233513037"}}}, "were_old_google_logins_removed": true}, "safebrowsing": {"enabled": false, "event_timestamps": {}, "metrics_last_log_time": "13398508138", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "search": {"suggest_enabled": false}, "segmentation_platform": {"client_result_prefs": "CuUCChFjcm9zc19kZXZpY2VfdXNlchLPAgrDAg0AAIA/ENic1rCIsOYXGrACCqcCGqQCChkNAACAPxISTm9Dcm9zc0RldmljZVVzYWdlChgNAAAAQBIRQ3Jvc3NEZXZpY2VNb2JpbGUKGQ0AAEBAEhJDcm9zc0RldmljZURlc2t0b3AKGA0AAIBAEhFDcm9zc0RldmljZVRhYmxldAoiDQAAoEASG0Nyb3NzRGV2aWNlTW9iaWxlQW5kRGVza3RvcAohDQAAwEASGkNyb3NzRGV2aWNlTW9iaWxlQW5kVGFibGV0CiINAADgQBIbQ3Jvc3NEZXZpY2VEZXNrdG9wQW5kVGFibGV0CiANAAAAQRIZQ3Jvc3NEZXZpY2VBbGxEZXZpY2VUeXBlcwoXDQAAEEESEENyb3NzRGV2aWNlT3RoZXISEk5vQ3Jvc3NEZXZpY2VVc2FnZRIEEAcYBCACEPOc1rCIsOYXClIKDXNob3BwaW5nX3VzZXISQQo2DQAAAAAQj7G4s/S75hcaJAocChoNAAAAPxIMU2hvcHBpbmdVc2VyGgVPdGhlchIEEAIYBCADEKrLpuGNvuYX", "device_switcher_util": {"result": {"labels": ["NotSynced"]}}, "last_db_compaction_time": "13398479999000000", "uma_in_sql_start_time": "13397980605628821"}, "sessions": {"event_log": [{"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13398488275002447", "type": 2, "window_count": 1}, {"crashed": false, "time": "13398488282213730", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13398488315296563", "type": 2, "window_count": 1}, {"crashed": false, "time": "13398508138627585", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13398508186047246", "type": 2, "window_count": 1}, {"crashed": false, "time": "13398508196276667", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13398508206255910", "type": 2, "window_count": 1}, {"crashed": false, "time": "13398508209680823", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13398508310289651", "type": 2, "window_count": 1}, {"crashed": false, "time": "13398508313728397", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13398508316955031", "type": 2, "window_count": 1}, {"crashed": false, "time": "13398508320424818", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13398508375318457", "type": 2, "window_count": 1}, {"crashed": false, "time": "13398508378714450", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13398508423055506", "type": 2, "window_count": 1}, {"crashed": false, "time": "13398583665182319", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "*****************", "type": 2, "window_count": 1}, {"crashed": false, "time": "*****************", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "*****************", "type": 2, "window_count": 1}], "session_data_status": 5}, "settings": {"force_google_safesearch": false}, "signin": {"allowed": true, "cookie_clear_on_exit_migration_notice_complete": true}, "spellcheck": {"dictionaries": ["en-US"], "dictionary": ""}, "sync": {"passwords_per_account_pref_migration_done": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "toolbar": {"pinned_cast_migration_complete": true, "pinned_chrome_labs_migration_complete": true}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 6, "translate": {"enabled": false}, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "138"}, "webrtc": {"ip_handling_policy": "disable_non_proxied_udp", "multiple_routes_enabled": false, "nonproxied_udp_enabled": false}}