[26404:25964:0802/124745.163:INFO:components\enterprise\browser\controller\chrome_browser_cloud_management_controller.cc:202] No machine level policy manager exists.
[25228:28816:0802/124745.712:VERBOSE1:components\viz\service\main\viz_main_impl.cc:86] VizNullHypothesis is disabled (not a warning)
[29816:27276:0802/124745.810:INFO:third_party\webrtc\rtc_base\cpu_info.cc:57] Available number of cores: 12
[24216:30264:0802/124745.811:INFO:third_party\webrtc\rtc_base\cpu_info.cc:57] Available number of cores: 12
[22692:30440:0802/124746.141:INFO:third_party\webrtc\rtc_base\cpu_info.cc:57] Available number of cores: 12
[28852:29760:0802/124747.629:INFO:third_party\webrtc\rtc_base\cpu_info.cc:57] Available number of cores: 12
[26404:25964:0802/124748.297:INFO:CONSOLE:40] "https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click", source: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click (40)
[26404:25964:0802/124748.389:INFO:CONSOLE:2] "%c [APLUS] -- APLUS INIT SUCCESS background:#3B82FE; padding: 4px; padding-right: 8px; border-radius: 4px; color: #fff;", source:  (2)
[26404:25964:0802/124748.631:INFO:CONSOLE:10] "null", source: https://g.alicdn.com/code/npm/@ali/isse-tfx-solution-plugin/1.7.0/??chunk-98eb0299.js,jsBlocks/head-block/index.js,chunk-09f9c16c.js,jsBlocks/root-wrapper/index.js (10)
[26404:25964:0802/124748.665:INFO:CONSOLE:19] "conf lib login", source: https://g.alicdn.com/??code/npm/@ali/universal-mtop/7.0.0/index.js,code/npm/@ali/pcom-tfx-common/1.7.2/vendor.cjs.es5.production.js,code/npm/@ali/pcom-tfx-common/1.7.2/index.js,code/npm/@ali/alimod-tfx-selectmarket-header/1.15.1/vendor.cjs.es5.production.js,code/npm/@ali/alimod-tfx-selectmarket-header/1.15.1/index.js,code/npm/@ali/alimod-tfx-selectmarket-header/1.15.1/index-pc.js,code/npm/@ali/pcom-tfx-item-card/1.7.0/vendor.cjs.es5.production.js,code/npm/@ali/pcom-tfx-item-card/1.7.0/index.js,code/npm/@ali/pcom-tfx-market-proform/1.0.0/index.js,code/npm/@ali/pnpm-uni--env/1.0.6/index.js,rax-pkg/universal-env/3.3.3/index.js,code/npm/@ali/universal-mtop/6.5.12/index.js,code/npm/@ali/alimod-tfx-item-market-search/1.14.1/vendor.cjs.es5.production.js,code/npm/@ali/alimod-tfx-item-market-search/1.14.1/index.js,code/npm/@ali/alimod-tfx-item-market-search/1.14.1/index-pc.js,code/npm/@ali/pcom-mtop/3.1.2/index.js,code/npm/@ali/pcom-dynamic/2.1.2/index.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-5481c64c.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-b67756ef.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-6a548963.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-ce9bf452.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-e1d0288e.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/addons/onDynamicDataSource/clientRequest.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-4f7c0bfa.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/addons/onDynamicDataSource/utils.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/addons/onDynamicDataSource/index.js,code/npm/@ali/multimod-ice-solution-tubes__page-theme/0.0.2/index.js,code/npm/@ali/multimod-ice-solution-tubes__floor-title/0.0.2/index.js,code/npm/@ali/isse-tfx-solution-plugin/1.7.0/chunk-f151e2b5.js (19)
[26404:25964:0802/124748.709:INFO:CONSOLE:2] "[APLUS] -- APLUS INIT SUCCESS", source: https://g.alicdn.com/??code/npm/@ali/universal-mtop/7.0.0/index.js,code/npm/@ali/pcom-tfx-common/1.7.2/vendor.cjs.es5.production.js,code/npm/@ali/pcom-tfx-common/1.7.2/index.js,code/npm/@ali/alimod-tfx-selectmarket-header/1.15.1/vendor.cjs.es5.production.js,code/npm/@ali/alimod-tfx-selectmarket-header/1.15.1/index.js,code/npm/@ali/alimod-tfx-selectmarket-header/1.15.1/index-pc.js,code/npm/@ali/pcom-tfx-item-card/1.7.0/vendor.cjs.es5.production.js,code/npm/@ali/pcom-tfx-item-card/1.7.0/index.js,code/npm/@ali/pcom-tfx-market-proform/1.0.0/index.js,code/npm/@ali/pnpm-uni--env/1.0.6/index.js,rax-pkg/universal-env/3.3.3/index.js,code/npm/@ali/universal-mtop/6.5.12/index.js,code/npm/@ali/alimod-tfx-item-market-search/1.14.1/vendor.cjs.es5.production.js,code/npm/@ali/alimod-tfx-item-market-search/1.14.1/index.js,code/npm/@ali/alimod-tfx-item-market-search/1.14.1/index-pc.js,code/npm/@ali/pcom-mtop/3.1.2/index.js,code/npm/@ali/pcom-dynamic/2.1.2/index.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-5481c64c.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-b67756ef.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-6a548963.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-ce9bf452.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-e1d0288e.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/addons/onDynamicDataSource/clientRequest.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-4f7c0bfa.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/addons/onDynamicDataSource/utils.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/addons/onDynamicDataSource/index.js,code/npm/@ali/multimod-ice-solution-tubes__page-theme/0.0.2/index.js,code/npm/@ali/multimod-ice-solution-tubes__floor-title/0.0.2/index.js,code/npm/@ali/isse-tfx-solution-plugin/1.7.0/chunk-f151e2b5.js (2)
[26404:25964:0802/124748.914:INFO:CONSOLE:146] "[object Object] queryGoodsList params", source: https://g.alicdn.com/??code/npm/@ali/universal-mtop/7.0.0/index.js,code/npm/@ali/pcom-tfx-common/1.7.2/vendor.cjs.es5.production.js,code/npm/@ali/pcom-tfx-common/1.7.2/index.js,code/npm/@ali/alimod-tfx-selectmarket-header/1.15.1/vendor.cjs.es5.production.js,code/npm/@ali/alimod-tfx-selectmarket-header/1.15.1/index.js,code/npm/@ali/alimod-tfx-selectmarket-header/1.15.1/index-pc.js,code/npm/@ali/pcom-tfx-item-card/1.7.0/vendor.cjs.es5.production.js,code/npm/@ali/pcom-tfx-item-card/1.7.0/index.js,code/npm/@ali/pcom-tfx-market-proform/1.0.0/index.js,code/npm/@ali/pnpm-uni--env/1.0.6/index.js,rax-pkg/universal-env/3.3.3/index.js,code/npm/@ali/universal-mtop/6.5.12/index.js,code/npm/@ali/alimod-tfx-item-market-search/1.14.1/vendor.cjs.es5.production.js,code/npm/@ali/alimod-tfx-item-market-search/1.14.1/index.js,code/npm/@ali/alimod-tfx-item-market-search/1.14.1/index-pc.js,code/npm/@ali/pcom-mtop/3.1.2/index.js,code/npm/@ali/pcom-dynamic/2.1.2/index.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-5481c64c.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-b67756ef.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-6a548963.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-ce9bf452.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-e1d0288e.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/addons/onDynamicDataSource/clientRequest.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-4f7c0bfa.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/addons/onDynamicDataSource/utils.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/addons/onDynamicDataSource/index.js,code/npm/@ali/multimod-ice-solution-tubes__page-theme/0.0.2/index.js,code/npm/@ali/multimod-ice-solution-tubes__floor-title/0.0.2/index.js,code/npm/@ali/isse-tfx-solution-plugin/1.7.0/chunk-f151e2b5.js (146)
[26404:25964:0802/124749.328:INFO:CONSOLE:0] "[DOM] Input elements should have autocomplete attributes (suggested: "current-password"): (More info: https://goo.gl/9p2vKq) %o", source: https://login.taobao.com/havanaone/login/login.htm?bizName=taobao&ttid=&redirectURL=https%3A%2F%2Fjingya-x.taobao.com%2Fwow%2Fz%2Ftfx%2Fstatic%2FdXCnMcpEntsE375tfHGp%3Fchannel%3Dglobal_zx%26spm%3Da21ug5.29159167.6452766900.2ndview_zxmore_click&sub=true (0)
[26404:25964:0802/124749.451:INFO:CONSOLE:3] "Error", source: https://g.alicdn.com/AWSC/et/1.83.35/et_f.js (3)
[26404:25964:0802/124749.599:INFO:CONSOLE:1] "Permissions policy violation: gyroscope is not allowed in this document.", source: https://g.alicdn.com/secdev/sufei_data/3.9.14/index.js (1)
[26404:25964:0802/124749.599:INFO:CONSOLE:1] "Permissions policy violation: accelerometer is not allowed in this document.", source: https://g.alicdn.com/secdev/sufei_data/3.9.14/index.js (1)
[26404:25964:0802/124749.599:INFO:CONSOLE:1] "The deviceorientation events are blocked by permissions policy. See https://github.com/w3c/webappsec-permissions-policy/blob/master/features.md#sensor-features", source: https://g.alicdn.com/secdev/sufei_data/3.9.14/index.js (1)
[31172:31176:0802/124749.601:INFO:third_party\webrtc\rtc_base\cpu_info.cc:57] Available number of cores: 12
[26404:25964:0802/124749.637:INFO:CONSOLE:1] "Error", source: https://g.alicdn.com/AWSC/fireyejs/1.231.61/fireyejs.js (1)
[26404:25964:0802/124749.691:INFO:CONSOLE:3] "Error", source: https://g.alicdn.com/AWSC/et/1.83.35/et_f.js (3)
[22692:31676:0802/124749.697:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[22692:31676:0802/124749.697:INFO:third_party\webrtc\media\engine\webrtc_voice_engine.cc:478] WebRtcVoiceEngine::WebRtcVoiceEngine
[22692:31680:0802/124749.697:INFO:third_party\webrtc\media\engine\webrtc_voice_engine.cc:500] WebRtcVoiceEngine::Init
[22692:31680:0802/124749.698:INFO:third_party\webrtc\media\engine\webrtc_voice_engine.cc:603] WebRtcVoiceEngine::ApplyOptions: AudioOptions {aec: 1, agc: 1, ns: 1, hf: 1, swap: 0, audio_jitter_buffer_max_packets: 200, audio_jitter_buffer_fast_accelerate: 0, audio_jitter_buffer_min_delay_ms: 0, }
[26404:25964:0802/124749.725:INFO:CONSOLE:3] "Error", source: https://g.alicdn.com/AWSC/et/1.83.35/et_f.js (3)
[22692:31676:0802/124750.203:INFO:third_party\webrtc\media\base\codec.cc:395] Explicitly added H264 constrained baseline to list of supported formats.
[22692:31680:0802/124750.204:INFO:third_party\webrtc\pc\peer_connection_factory.cc:412] Using default network controller factory
[22692:31680:0802/124750.204:INFO:third_party\webrtc\modules\pacing\bitrate_prober.cc:54] Bandwidth probing enabled, set to inactive
[22692:31680:0802/124750.204:INFO:third_party\webrtc\modules\remote_bitrate_estimator\transport_sequence_number_feedback_generator.cc:52] Maximum interval between transport feedback RTCP messages: 250 ms
[22692:31680:0802/124750.204:INFO:third_party\webrtc\modules\remote_bitrate_estimator\aimd_rate_control.cc:88] Using aimd rate control with back off factor 0.85
[22692:31680:0802/124750.204:INFO:third_party\webrtc\modules\remote_bitrate_estimator\remote_bitrate_estimator_single_stream.cc:59] RemoteBitrateEstimatorSingleStream: Instantiating.
[22692:31676:0802/124750.204:INFO:third_party\webrtc\media\base\codec.cc:395] Explicitly added H264 constrained baseline to list of supported formats.
[22692:31680:0802/124750.204:INFO:third_party\webrtc\rtc_base\openssl_key_pair.cc:40] Making key pair
[22692:31680:0802/124750.205:INFO:third_party\webrtc\rtc_base\openssl_key_pair.cc:93] Returning key pair
[22692:31680:0802/124750.205:INFO:third_party\webrtc\rtc_base\boringssl_certificate.cc:193] Making certificate for WebRTC
[22692:31680:0802/124750.205:INFO:third_party\webrtc\rtc_base\boringssl_certificate.cc:249] Returning certificate
[22692:31680:0802/124750.208:INFO:third_party\webrtc\p2p\base\p2p_transport_channel.cc:546] Set backup connection ping interval to 25000 milliseconds.
[22692:31680:0802/124750.208:INFO:third_party\webrtc\p2p\base\p2p_transport_channel.cc:555] Set ICE receiving timeout to 2500 milliseconds
[22692:31680:0802/124750.208:INFO:third_party\webrtc\p2p\base\p2p_transport_channel.cc:562] Set ping most likely connection to 0
[22692:31680:0802/124750.208:INFO:third_party\webrtc\p2p\base\p2p_transport_channel.cc:569] Set stable_writable_connection_ping_interval to 2500
[22692:31680:0802/124750.208:INFO:third_party\webrtc\p2p\base\p2p_transport_channel.cc:582] Set presume writable when fully relayed to 0
[22692:31680:0802/124750.208:INFO:third_party\webrtc\p2p\base\p2p_transport_channel.cc:600] Set regather_on_failed_networks_interval to 300000
[22692:31680:0802/124750.208:INFO:third_party\webrtc\p2p\base\p2p_transport_channel.cc:607] Set receiving_switching_delay to 1000
[22692:31680:0802/124750.209:INFO:third_party\webrtc\pc\jsep_transport_controller.cc:1184] Creating DtlsSrtpTransport.
[22692:31680:0802/124750.209:INFO:third_party\webrtc\pc\dtls_srtp_transport.cc:72] Setting RTP Transport on data transport 0x70e407086000
[22692:31680:0802/124750.210:WARNING:third_party\webrtc\p2p\base\transport_description.cc:39] '-', '=', '#' and '-' are not valid ice-char and thus not permitted in ufrag or pwd. This is a protocol violation that is permitted to allow upgrading but will be rejected in the future. See https://crbug.com/1053756
[22692:31680:0802/124750.210:WARNING:third_party\webrtc\p2p\base\transport_description.cc:39] '-', '=', '#' and '-' are not valid ice-char and thus not permitted in ufrag or pwd. This is a protocol violation that is permitted to allow upgrading but will be rejected in the future. See https://crbug.com/1053756
[22692:31680:0802/124750.210:WARNING:third_party\webrtc\p2p\base\transport_description.cc:39] '-', '=', '#' and '-' are not valid ice-char and thus not permitted in ufrag or pwd. This is a protocol violation that is permitted to allow upgrading but will be rejected in the future. See https://crbug.com/1053756
[22692:31680:0802/124750.210:INFO:third_party\webrtc\p2p\base\p2p_transport_channel.cc:491] Received remote ICE parameters: ufrag=0068T2gA9hYPvBO3IKK_NvDOpUzvUGjo3DtHZ9NvC5A3PXZayIzoFHikxAae5tK-TM1EEys=, renomination disabled
[22692:31676:0802/124750.210:INFO:third_party\webrtc\pc\peer_connection.cc:2085] Creating data channel, mid=data
[22692:31676:0802/124750.210:INFO:third_party\webrtc\pc\sdp_offer_answer.cc:3092] Session: 953134944696117162 Old state: stable New state: have-remote-offer
[26404:25964:0802/124750.229:INFO:CONSOLE:3] "Error", source: https://g.alicdn.com/AWSC/et/1.83.35/et_f.js (3)
[26404:25964:0802/124750.251:INFO:CONSOLE:1] "Error", source: https://g.alicdn.com/AWSC/fireyejs/1.231.61/fireyejs.js (1)
[26404:25964:0802/124750.281:INFO:CONSOLE:3] "Error", source: https://g.alicdn.com/AWSC/et/1.83.35/et_f.js (3)
[22692:31680:0802/124750.284:INFO:third_party\webrtc\pc\peer_connection_factory.cc:412] Using default network controller factory
[22692:31680:0802/124750.284:INFO:third_party\webrtc\modules\pacing\bitrate_prober.cc:54] Bandwidth probing enabled, set to inactive
[22692:31680:0802/124750.284:INFO:third_party\webrtc\modules\remote_bitrate_estimator\transport_sequence_number_feedback_generator.cc:52] Maximum interval between transport feedback RTCP messages: 250 ms
[22692:31680:0802/124750.284:INFO:third_party\webrtc\modules\remote_bitrate_estimator\aimd_rate_control.cc:88] Using aimd rate control with back off factor 0.85
[22692:31680:0802/124750.284:INFO:third_party\webrtc\modules\remote_bitrate_estimator\remote_bitrate_estimator_single_stream.cc:59] RemoteBitrateEstimatorSingleStream: Instantiating.
[22692:31676:0802/124750.284:INFO:third_party\webrtc\media\base\codec.cc:395] Explicitly added H264 constrained baseline to list of supported formats.
[22692:31680:0802/124750.285:INFO:third_party\webrtc\rtc_base\openssl_key_pair.cc:40] Making key pair
[22692:31680:0802/124750.285:INFO:third_party\webrtc\rtc_base\openssl_key_pair.cc:93] Returning key pair
[22692:31680:0802/124750.285:INFO:third_party\webrtc\rtc_base\boringssl_certificate.cc:193] Making certificate for WebRTC
[22692:31680:0802/124750.285:INFO:third_party\webrtc\rtc_base\boringssl_certificate.cc:249] Returning certificate
[22692:31680:0802/124750.286:INFO:third_party\webrtc\pc\peer_connection_factory.cc:412] Using default network controller factory
[22692:31680:0802/124750.286:INFO:third_party\webrtc\modules\pacing\bitrate_prober.cc:54] Bandwidth probing enabled, set to inactive
[22692:31680:0802/124750.286:INFO:third_party\webrtc\modules\remote_bitrate_estimator\transport_sequence_number_feedback_generator.cc:52] Maximum interval between transport feedback RTCP messages: 250 ms
[22692:31680:0802/124750.286:INFO:third_party\webrtc\modules\remote_bitrate_estimator\aimd_rate_control.cc:88] Using aimd rate control with back off factor 0.85
[22692:31680:0802/124750.286:INFO:third_party\webrtc\modules\remote_bitrate_estimator\remote_bitrate_estimator_single_stream.cc:59] RemoteBitrateEstimatorSingleStream: Instantiating.
[22692:31676:0802/124750.286:INFO:third_party\webrtc\media\base\codec.cc:395] Explicitly added H264 constrained baseline to list of supported formats.
[22692:31680:0802/124750.287:INFO:third_party\webrtc\rtc_base\openssl_key_pair.cc:40] Making key pair
[22692:31680:0802/124750.287:INFO:third_party\webrtc\rtc_base\openssl_key_pair.cc:93] Returning key pair
[22692:31680:0802/124750.287:INFO:third_party\webrtc\rtc_base\boringssl_certificate.cc:193] Making certificate for WebRTC
[22692:31680:0802/124750.287:INFO:third_party\webrtc\rtc_base\boringssl_certificate.cc:249] Returning certificate
[26404:25964:0802/124750.289:INFO:CONSOLE:3] "Error", source: https://g.alicdn.com/AWSC/et/1.83.35/et_f.js (3)
[22692:31680:0802/124750.293:INFO:third_party\webrtc\pc\dtls_srtp_transport.cc:72] Setting RTP Transport on data transport 0x70e407086000
[22692:31680:0802/124750.293:INFO:third_party\webrtc\p2p\base\p2p_transport_channel.cc:480] Set ICE ufrag: B6Nh pwd: sX4aD+MVJUPSrYMoB/NRx3Lw on transport data
[22692:31680:0802/124750.293:WARNING:third_party\webrtc\rtc_base\openssl_stream_adapter.cc:148] WebRTC-ForceDtls13: 
[22692:31680:0802/124750.293:INFO:third_party\webrtc\p2p\dtls\dtls_transport.cc:497] DtlsTransport[data|1|__]: DTLS setup complete, dtls_in_stun: 0
[22692:31676:0802/124750.293:INFO:third_party\webrtc\pc\peer_connection.cc:2085] Creating data channel, mid=data
[22692:31676:0802/124750.293:INFO:third_party\webrtc\pc\sdp_offer_answer.cc:3092] Session: 953134944696117162 Old state: have-remote-offer New state: stable
[22692:31680:0802/124750.293:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:367] Start getting ports with turn_port_prune_policy 0
[22692:31680:0802/124750.294:INFO:third_party\webrtc\p2p\base\p2p_transport_channel.cc:1189] Asynchronously resolving ICE candidate hostname ph5.ynuf.aliapp.org
[22692:31676:0802/124750.294:INFO:third_party\webrtc\pc\peer_connection.cc:1867] Changing IceConnectionState 0 => 1
[22692:31680:0802/124750.295:INFO:third_party\webrtc\p2p\base\p2p_transport_channel.cc:546] Set backup connection ping interval to 25000 milliseconds.
[22692:31680:0802/124750.295:INFO:third_party\webrtc\p2p\base\p2p_transport_channel.cc:555] Set ICE receiving timeout to 2500 milliseconds
[22692:31680:0802/124750.295:INFO:third_party\webrtc\p2p\base\p2p_transport_channel.cc:562] Set ping most likely connection to 0
[22692:31680:0802/124750.295:INFO:third_party\webrtc\p2p\base\p2p_transport_channel.cc:569] Set stable_writable_connection_ping_interval to 2500
[22692:31680:0802/124750.295:INFO:third_party\webrtc\p2p\base\p2p_transport_channel.cc:582] Set presume writable when fully relayed to 0
[22692:31680:0802/124750.295:INFO:third_party\webrtc\p2p\base\p2p_transport_channel.cc:600] Set regather_on_failed_networks_interval to 300000
[22692:31680:0802/124750.295:INFO:third_party\webrtc\p2p\base\p2p_transport_channel.cc:607] Set receiving_switching_delay to 1000
[22692:31680:0802/124750.295:INFO:third_party\webrtc\pc\jsep_transport_controller.cc:1184] Creating DtlsSrtpTransport.
[22692:31680:0802/124750.295:INFO:third_party\webrtc\pc\dtls_srtp_transport.cc:72] Setting RTP Transport on 0 transport 0x70e40c1f6400
[22692:31680:0802/124750.295:INFO:third_party\webrtc\p2p\base\p2p_transport_channel.cc:480] Set ICE ufrag: MQoz pwd: FBSm4/wFq03xovBzqOWdjo/K on transport 0
[22692:31676:0802/124750.295:INFO:third_party\webrtc\pc\peer_connection.cc:2085] Creating data channel, mid=0
[22692:31676:0802/124750.295:INFO:third_party\webrtc\pc\sdp_offer_answer.cc:3092] Session: 1393415258734164413 Old state: stable New state: have-local-offer
[22692:31680:0802/124750.295:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:367] Start getting ports with turn_port_prune_policy 0
[22692:31680:0802/124750.296:INFO:third_party\webrtc\p2p\base\p2p_transport_channel.cc:546] Set backup connection ping interval to 25000 milliseconds.
[22692:31680:0802/124750.296:INFO:third_party\webrtc\p2p\base\p2p_transport_channel.cc:555] Set ICE receiving timeout to 2500 milliseconds
[22692:31680:0802/124750.296:INFO:third_party\webrtc\p2p\base\p2p_transport_channel.cc:562] Set ping most likely connection to 0
[22692:31680:0802/124750.296:INFO:third_party\webrtc\p2p\base\p2p_transport_channel.cc:569] Set stable_writable_connection_ping_interval to 2500
[22692:31680:0802/124750.296:INFO:third_party\webrtc\p2p\base\p2p_transport_channel.cc:582] Set presume writable when fully relayed to 0
[22692:31680:0802/124750.296:INFO:third_party\webrtc\p2p\base\p2p_transport_channel.cc:600] Set regather_on_failed_networks_interval to 300000
[22692:31680:0802/124750.296:INFO:third_party\webrtc\p2p\base\p2p_transport_channel.cc:607] Set receiving_switching_delay to 1000
[22692:31680:0802/124750.296:INFO:third_party\webrtc\pc\jsep_transport_controller.cc:1184] Creating DtlsSrtpTransport.
[22692:31680:0802/124750.296:INFO:third_party\webrtc\pc\dtls_srtp_transport.cc:72] Setting RTP Transport on 0 transport 0x70e40c1f7800
[22692:31680:0802/124750.296:INFO:third_party\webrtc\p2p\base\p2p_transport_channel.cc:480] Set ICE ufrag: +UHJ pwd: kvIihC1ESuEABm0gV+TgIGzh on transport 0
[22692:31676:0802/124750.296:INFO:third_party\webrtc\pc\peer_connection.cc:2085] Creating data channel, mid=0
[22692:31676:0802/124750.296:INFO:third_party\webrtc\pc\sdp_offer_answer.cc:3092] Session: 3780870962951813998 Old state: stable New state: have-local-offer
[22692:31680:0802/124750.296:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:367] Start getting ports with turn_port_prune_policy 0
[22692:31680:0802/124750.302:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:809] Allocate ports on any any 
[22692:31680:0802/124750.302:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:899] Network manager has started
[22692:31680:0802/124750.302:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:809] Allocate ports on any any 
[22692:31680:0802/124750.302:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:899] Network manager has started
[22692:31680:0802/124750.302:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:809] Allocate ports on any any 
[22692:31680:0802/124750.302:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:899] Network manager has started
[22692:31680:0802/124750.302:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:1408] Net[any:0.0.0.x/0:Wildcard:id=0]: Allocation Phase=Udp
[22692:31680:0802/124750.302:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:1408] Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]: Allocation Phase=Udp
[22692:31680:0802/124750.302:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:1408] Net[any:0.0.0.x/0:Wildcard:id=0]: Allocation Phase=Udp
[22692:31680:0802/124750.302:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:1408] Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]: Allocation Phase=Udp
[22692:31680:0802/124750.302:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:1408] Net[any:0.0.0.x/0:Wildcard:id=0]: Allocation Phase=Udp
[22692:31680:0802/124750.302:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:1408] Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]: Allocation Phase=Udp
[22692:31680:0802/124750.355:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:1408] Net[any:0.0.0.x/0:Wildcard:id=0]: Allocation Phase=Relay
[22692:31680:0802/124750.355:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:1408] Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]: Allocation Phase=Relay
[22692:31680:0802/124750.355:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:1408] Net[any:0.0.0.x/0:Wildcard:id=0]: Allocation Phase=Relay
[22692:31680:0802/124750.355:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:1408] Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]: Allocation Phase=Relay
[22692:31680:0802/124750.355:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:1408] Net[any:0.0.0.x/0:Wildcard:id=0]: Allocation Phase=Relay
[22692:31680:0802/124750.355:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:1408] Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]: Allocation Phase=Relay
[26404:25964:0802/124750.404:INFO:CONSOLE:3] "Error", source: https://g.alicdn.com/AWSC/et/1.83.35/et_f.js (3)
[22692:31680:0802/124750.406:INFO:third_party\webrtc\p2p\base\p2p_transport_channel.cc:1296] Resolved ICE candidate hostname ph5.ynuf.aliapp.org to 203.119.204.x
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:1408] Net[any:0.0.0.x/0:Wildcard:id=0]: Allocation Phase=Tcp
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\base\port.cc:146] Port[c1f7400::1:0:host:Net[any:0.0.0.x/0:Wildcard:id=0]]: Port created with network cost 999
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:922] Adding allocated port for data
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:942] Port[c1f7400:data:1:0:host:Net[any:0.0.0.x/0:Wildcard:id=0]]: Added port to allocator
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\base\tcp_port.cc:185] Port[c1f7400:data:1:0:host:Net[any:0.0.0.x/0:Wildcard:id=0]]: Not listening due to firewall restrictions.
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:959] Port[c1f7400:data:1:0:host:Net[any:0.0.0.x/0:Wildcard:id=0]]: Gathered candidate: Cand[:239740417:1:tcp:1509957375:0.0.0.x:9:host::0:B6Nh:sX4aD+MVJUPSrYMoB/NRx3Lw:0:999:0]
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:992] Port[c1f7400:data:1:0:host:Net[any:0.0.0.x/0:Wildcard:id=0]]: Port ready.
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:1003] Discarding candidate because it doesn't match filter.
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:1104] Port[c1f7400:data:1:0:host:Net[any:0.0.0.x/0:Wildcard:id=0]]: Port completed gathering candidates.
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:1408] Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]: Allocation Phase=Tcp
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\base\port.cc:146] Port[fd6cc00::1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: Port created with network cost 999
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:922] Adding allocated port for data
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:942] Port[fd6cc00:data:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: Added port to allocator
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\base\tcp_port.cc:185] Port[fd6cc00:data:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: Not listening due to firewall restrictions.
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:959] Port[fd6cc00:data:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: Gathered candidate: Cand[:191218426:1:tcp:1509949951:[0:0:0:x:x:x:x:x]:9:host::0:B6Nh:sX4aD+MVJUPSrYMoB/NRx3Lw:0:999:0]
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:992] Port[fd6cc00:data:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: Port ready.
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:1003] Discarding candidate because it doesn't match filter.
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:1104] Port[fd6cc00:data:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: Port completed gathering candidates.
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:1178] All candidates gathered for data:1:0
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\base\p2p_transport_channel.cc:964] P2PTransportChannel: data, component 1 gathering complete
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:1408] Net[any:0.0.0.x/0:Wildcard:id=0]: Allocation Phase=Tcp
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\base\port.cc:146] Port[fd6c400::1:0:host:Net[any:0.0.0.x/0:Wildcard:id=0]]: Port created with network cost 999
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:922] Adding allocated port for 0
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:942] Port[fd6c400:0:1:0:host:Net[any:0.0.0.x/0:Wildcard:id=0]]: Added port to allocator
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\base\tcp_port.cc:185] Port[fd6c400:0:1:0:host:Net[any:0.0.0.x/0:Wildcard:id=0]]: Not listening due to firewall restrictions.
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:959] Port[fd6c400:0:1:0:host:Net[any:0.0.0.x/0:Wildcard:id=0]]: Gathered candidate: Cand[:2195930903:1:tcp:1509957375:0.0.0.x:9:host::0:MQoz:FBSm4/wFq03xovBzqOWdjo/K:0:999:0]
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:992] Port[fd6c400:0:1:0:host:Net[any:0.0.0.x/0:Wildcard:id=0]]: Port ready.
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:1003] Discarding candidate because it doesn't match filter.
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:1104] Port[fd6c400:0:1:0:host:Net[any:0.0.0.x/0:Wildcard:id=0]]: Port completed gathering candidates.
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:1408] Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]: Allocation Phase=Tcp
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\base\port.cc:146] Port[fd6f000::1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: Port created with network cost 999
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:922] Adding allocated port for 0
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:942] Port[fd6f000:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: Added port to allocator
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\base\tcp_port.cc:185] Port[fd6f000:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: Not listening due to firewall restrictions.
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:959] Port[fd6f000:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: Gathered candidate: Cand[:2907756496:1:tcp:1509949951:[0:0:0:x:x:x:x:x]:9:host::0:MQoz:FBSm4/wFq03xovBzqOWdjo/K:0:999:0]
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:992] Port[fd6f000:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: Port ready.
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:1003] Discarding candidate because it doesn't match filter.
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:1104] Port[fd6f000:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: Port completed gathering candidates.
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:1178] All candidates gathered for 0:1:0
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\base\p2p_transport_channel.cc:964] P2PTransportChannel: 0, component 1 gathering complete
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:1408] Net[any:0.0.0.x/0:Wildcard:id=0]: Allocation Phase=Tcp
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\base\port.cc:146] Port[fd6e800::1:0:host:Net[any:0.0.0.x/0:Wildcard:id=0]]: Port created with network cost 999
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:922] Adding allocated port for 0
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:942] Port[fd6e800:0:1:0:host:Net[any:0.0.0.x/0:Wildcard:id=0]]: Added port to allocator
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\base\tcp_port.cc:185] Port[fd6e800:0:1:0:host:Net[any:0.0.0.x/0:Wildcard:id=0]]: Not listening due to firewall restrictions.
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:959] Port[fd6e800:0:1:0:host:Net[any:0.0.0.x/0:Wildcard:id=0]]: Gathered candidate: Cand[:2483423878:1:tcp:1509957375:0.0.0.x:9:host::0:+UHJ:kvIihC1ESuEABm0gV+TgIGzh:0:999:0]
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:992] Port[fd6e800:0:1:0:host:Net[any:0.0.0.x/0:Wildcard:id=0]]: Port ready.
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:1003] Discarding candidate because it doesn't match filter.
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:1104] Port[fd6e800:0:1:0:host:Net[any:0.0.0.x/0:Wildcard:id=0]]: Port completed gathering candidates.
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:1408] Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]: Allocation Phase=Tcp
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\base\port.cc:146] Port[fd6c000::1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: Port created with network cost 999
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:922] Adding allocated port for 0
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:942] Port[fd6c000:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: Added port to allocator
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\base\tcp_port.cc:185] Port[fd6c000:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: Not listening due to firewall restrictions.
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:959] Port[fd6c000:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: Gathered candidate: Cand[:2435444349:1:tcp:1509949951:[0:0:0:x:x:x:x:x]:9:host::0:+UHJ:kvIihC1ESuEABm0gV+TgIGzh:0:999:0]
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:992] Port[fd6c000:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: Port ready.
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:1003] Discarding candidate because it doesn't match filter.
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:1104] Port[fd6c000:0:1:0:host:Net[any:0:0:0:x:x:x:x:x/0:Wildcard:id=0]]: Port completed gathering candidates.
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\client\basic_port_allocator.cc:1178] All candidates gathered for 0:1:0
[22692:31680:0802/124750.417:INFO:third_party\webrtc\p2p\base\p2p_transport_channel.cc:964] P2PTransportChannel: 0, component 1 gathering complete
[22692:31676:0802/124750.417:INFO:third_party\webrtc\pc\sdp_offer_answer.cc:3092] Session: 953134944696117162 Old state: stable New state: closed
[22692:31680:0802/124750.417:INFO:third_party\webrtc\pc\peer_connection.cc:2542] Tearing down data channel transport for mid=data
[26404:25964:0802/124750.428:INFO:CONSOLE:3] "Error", source: https://g.alicdn.com/AWSC/et/1.83.35/et_f.js (3)
[26404:25964:0802/124750.891:INFO:CONSOLE:1] "The ScriptProcessorNode is deprecated. Use AudioWorkletNode instead. (https://bit.ly/audio-worklet)", source: https://g.alicdn.com/AWSC/fireyejs/1.231.61/fireyejs.js (1)
[26404:25964:0802/124750.902:INFO:CONSOLE:3] "Error", source: https://g.alicdn.com/AWSC/et/1.83.35/et_f.js (3)
[26404:25964:0802/124750.905:INFO:CONSOLE:1] "Error", source: https://g.alicdn.com/AWSC/fireyejs/1.231.61/fireyejs.js (1)
[26404:25964:0802/124750.927:INFO:CONSOLE:3] "Error", source: https://g.alicdn.com/AWSC/et/1.83.35/et_f.js (3)
[26404:25964:0802/124750.935:INFO:CONSOLE:3] "Error", source: https://g.alicdn.com/AWSC/et/1.83.35/et_f.js (3)
[22692:31676:0802/124751.287:INFO:third_party\webrtc\pc\sdp_offer_answer.cc:3092] Session: 1393415258734164413 Old state: have-local-offer New state: closed
[22692:31680:0802/124751.287:INFO:third_party\webrtc\pc\peer_connection.cc:2542] Tearing down data channel transport for mid=0
[22692:31676:0802/124751.288:INFO:third_party\webrtc\pc\sdp_offer_answer.cc:3092] Session: 3780870962951813998 Old state: have-local-offer New state: closed
[22692:31680:0802/124751.288:INFO:third_party\webrtc\pc\peer_connection.cc:2542] Tearing down data channel transport for mid=0
[26404:25964:0802/124751.331:INFO:CONSOLE:14] "桌面版User-Agent已设置: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", source:  (14)
[26404:25964:0802/124751.733:INFO:CONSOLE:1] "Error", source: https://g.alicdn.com/AWSC/fireyejs/1.231.61/fireyejs.js (1)
[26404:25964:0802/124751.846:INFO:CONSOLE:3] "Error", source: https://g.alicdn.com/AWSC/et/1.83.35/et_f.js (3)
[26404:25964:0802/124751.862:INFO:CONSOLE:40] "https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click", source: https://jingya-x.taobao.com/wow/z/tfx/static/dXCnMcpEntsE375tfHGp?channel=global_zx&spm=a21ug5.29159167.6452766900.2ndview_zxmore_click (40)
[26404:25964:0802/124751.865:INFO:CONSOLE:3] "Error", source: https://g.alicdn.com/AWSC/et/1.83.35/et_f.js (3)
[26404:25964:0802/124751.985:INFO:CONSOLE:10] "[object Object]", source: https://g.alicdn.com/code/npm/@ali/isse-tfx-solution-plugin/1.7.0/??chunk-98eb0299.js,jsBlocks/head-block/index.js,chunk-09f9c16c.js,jsBlocks/root-wrapper/index.js (10)
[26404:25964:0802/124751.992:INFO:CONSOLE:19] "conf lib login", source: https://g.alicdn.com/??code/npm/@ali/universal-mtop/7.0.0/index.js,code/npm/@ali/pcom-tfx-common/1.7.2/vendor.cjs.es5.production.js,code/npm/@ali/pcom-tfx-common/1.7.2/index.js,code/npm/@ali/alimod-tfx-selectmarket-header/1.15.1/vendor.cjs.es5.production.js,code/npm/@ali/alimod-tfx-selectmarket-header/1.15.1/index.js,code/npm/@ali/alimod-tfx-selectmarket-header/1.15.1/index-pc.js,code/npm/@ali/pcom-tfx-item-card/1.7.0/vendor.cjs.es5.production.js,code/npm/@ali/pcom-tfx-item-card/1.7.0/index.js,code/npm/@ali/pcom-tfx-market-proform/1.0.0/index.js,code/npm/@ali/pnpm-uni--env/1.0.6/index.js,rax-pkg/universal-env/3.3.3/index.js,code/npm/@ali/universal-mtop/6.5.12/index.js,code/npm/@ali/alimod-tfx-item-market-search/1.14.1/vendor.cjs.es5.production.js,code/npm/@ali/alimod-tfx-item-market-search/1.14.1/index.js,code/npm/@ali/alimod-tfx-item-market-search/1.14.1/index-pc.js,code/npm/@ali/pcom-mtop/3.1.2/index.js,code/npm/@ali/pcom-dynamic/2.1.2/index.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-5481c64c.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-b67756ef.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-6a548963.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-ce9bf452.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-e1d0288e.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/addons/onDynamicDataSource/clientRequest.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-4f7c0bfa.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/addons/onDynamicDataSource/utils.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/addons/onDynamicDataSource/index.js,code/npm/@ali/multimod-ice-solution-tubes__page-theme/0.0.2/index.js,code/npm/@ali/multimod-ice-solution-tubes__floor-title/0.0.2/index.js,code/npm/@ali/isse-tfx-solution-plugin/1.7.0/chunk-f151e2b5.js (19)
[26404:25964:0802/124752.005:INFO:CONSOLE:2] "[APLUS] -- APLUS INIT SUCCESS", source: https://g.alicdn.com/??code/npm/@ali/universal-mtop/7.0.0/index.js,code/npm/@ali/pcom-tfx-common/1.7.2/vendor.cjs.es5.production.js,code/npm/@ali/pcom-tfx-common/1.7.2/index.js,code/npm/@ali/alimod-tfx-selectmarket-header/1.15.1/vendor.cjs.es5.production.js,code/npm/@ali/alimod-tfx-selectmarket-header/1.15.1/index.js,code/npm/@ali/alimod-tfx-selectmarket-header/1.15.1/index-pc.js,code/npm/@ali/pcom-tfx-item-card/1.7.0/vendor.cjs.es5.production.js,code/npm/@ali/pcom-tfx-item-card/1.7.0/index.js,code/npm/@ali/pcom-tfx-market-proform/1.0.0/index.js,code/npm/@ali/pnpm-uni--env/1.0.6/index.js,rax-pkg/universal-env/3.3.3/index.js,code/npm/@ali/universal-mtop/6.5.12/index.js,code/npm/@ali/alimod-tfx-item-market-search/1.14.1/vendor.cjs.es5.production.js,code/npm/@ali/alimod-tfx-item-market-search/1.14.1/index.js,code/npm/@ali/alimod-tfx-item-market-search/1.14.1/index-pc.js,code/npm/@ali/pcom-mtop/3.1.2/index.js,code/npm/@ali/pcom-dynamic/2.1.2/index.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-5481c64c.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-b67756ef.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-6a548963.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-ce9bf452.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-e1d0288e.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/addons/onDynamicDataSource/clientRequest.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-4f7c0bfa.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/addons/onDynamicDataSource/utils.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/addons/onDynamicDataSource/index.js,code/npm/@ali/multimod-ice-solution-tubes__page-theme/0.0.2/index.js,code/npm/@ali/multimod-ice-solution-tubes__floor-title/0.0.2/index.js,code/npm/@ali/isse-tfx-solution-plugin/1.7.0/chunk-f151e2b5.js (2)
[26404:25964:0802/124752.072:INFO:CONSOLE:2] "%c [APLUS] -- APLUS INIT SUCCESS background:#3B82FE; padding: 4px; padding-right: 8px; border-radius: 4px; color: #fff;", source:  (2)
[26404:25964:0802/124752.175:INFO:CONSOLE:146] "[object Object] queryGoodsList params", source: https://g.alicdn.com/??code/npm/@ali/universal-mtop/7.0.0/index.js,code/npm/@ali/pcom-tfx-common/1.7.2/vendor.cjs.es5.production.js,code/npm/@ali/pcom-tfx-common/1.7.2/index.js,code/npm/@ali/alimod-tfx-selectmarket-header/1.15.1/vendor.cjs.es5.production.js,code/npm/@ali/alimod-tfx-selectmarket-header/1.15.1/index.js,code/npm/@ali/alimod-tfx-selectmarket-header/1.15.1/index-pc.js,code/npm/@ali/pcom-tfx-item-card/1.7.0/vendor.cjs.es5.production.js,code/npm/@ali/pcom-tfx-item-card/1.7.0/index.js,code/npm/@ali/pcom-tfx-market-proform/1.0.0/index.js,code/npm/@ali/pnpm-uni--env/1.0.6/index.js,rax-pkg/universal-env/3.3.3/index.js,code/npm/@ali/universal-mtop/6.5.12/index.js,code/npm/@ali/alimod-tfx-item-market-search/1.14.1/vendor.cjs.es5.production.js,code/npm/@ali/alimod-tfx-item-market-search/1.14.1/index.js,code/npm/@ali/alimod-tfx-item-market-search/1.14.1/index-pc.js,code/npm/@ali/pcom-mtop/3.1.2/index.js,code/npm/@ali/pcom-dynamic/2.1.2/index.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-5481c64c.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-b67756ef.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-6a548963.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-ce9bf452.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-e1d0288e.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/addons/onDynamicDataSource/clientRequest.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-4f7c0bfa.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/addons/onDynamicDataSource/utils.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/addons/onDynamicDataSource/index.js,code/npm/@ali/multimod-ice-solution-tubes__page-theme/0.0.2/index.js,code/npm/@ali/multimod-ice-solution-tubes__floor-title/0.0.2/index.js,code/npm/@ali/isse-tfx-solution-plugin/1.7.0/chunk-f151e2b5.js (146)
[26404:25964:0802/124752.204:INFO:CONSOLE:19] "🚀 ~ getUserInfo ~ result: [object Object]", source: https://g.alicdn.com/??code/npm/@ali/universal-mtop/7.0.0/index.js,code/npm/@ali/pcom-tfx-common/1.7.2/vendor.cjs.es5.production.js,code/npm/@ali/pcom-tfx-common/1.7.2/index.js,code/npm/@ali/alimod-tfx-selectmarket-header/1.15.1/vendor.cjs.es5.production.js,code/npm/@ali/alimod-tfx-selectmarket-header/1.15.1/index.js,code/npm/@ali/alimod-tfx-selectmarket-header/1.15.1/index-pc.js,code/npm/@ali/pcom-tfx-item-card/1.7.0/vendor.cjs.es5.production.js,code/npm/@ali/pcom-tfx-item-card/1.7.0/index.js,code/npm/@ali/pcom-tfx-market-proform/1.0.0/index.js,code/npm/@ali/pnpm-uni--env/1.0.6/index.js,rax-pkg/universal-env/3.3.3/index.js,code/npm/@ali/universal-mtop/6.5.12/index.js,code/npm/@ali/alimod-tfx-item-market-search/1.14.1/vendor.cjs.es5.production.js,code/npm/@ali/alimod-tfx-item-market-search/1.14.1/index.js,code/npm/@ali/alimod-tfx-item-market-search/1.14.1/index-pc.js,code/npm/@ali/pcom-mtop/3.1.2/index.js,code/npm/@ali/pcom-dynamic/2.1.2/index.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-5481c64c.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-b67756ef.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-6a548963.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-ce9bf452.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-e1d0288e.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/addons/onDynamicDataSource/clientRequest.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-4f7c0bfa.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/addons/onDynamicDataSource/utils.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/addons/onDynamicDataSource/index.js,code/npm/@ali/multimod-ice-solution-tubes__page-theme/0.0.2/index.js,code/npm/@ali/multimod-ice-solution-tubes__floor-title/0.0.2/index.js,code/npm/@ali/isse-tfx-solution-plugin/1.7.0/chunk-f151e2b5.js (19)
[26404:25964:0802/124752.956:INFO:CONSOLE:146] "[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object] finalList", source: https://g.alicdn.com/??code/npm/@ali/universal-mtop/7.0.0/index.js,code/npm/@ali/pcom-tfx-common/1.7.2/vendor.cjs.es5.production.js,code/npm/@ali/pcom-tfx-common/1.7.2/index.js,code/npm/@ali/alimod-tfx-selectmarket-header/1.15.1/vendor.cjs.es5.production.js,code/npm/@ali/alimod-tfx-selectmarket-header/1.15.1/index.js,code/npm/@ali/alimod-tfx-selectmarket-header/1.15.1/index-pc.js,code/npm/@ali/pcom-tfx-item-card/1.7.0/vendor.cjs.es5.production.js,code/npm/@ali/pcom-tfx-item-card/1.7.0/index.js,code/npm/@ali/pcom-tfx-market-proform/1.0.0/index.js,code/npm/@ali/pnpm-uni--env/1.0.6/index.js,rax-pkg/universal-env/3.3.3/index.js,code/npm/@ali/universal-mtop/6.5.12/index.js,code/npm/@ali/alimod-tfx-item-market-search/1.14.1/vendor.cjs.es5.production.js,code/npm/@ali/alimod-tfx-item-market-search/1.14.1/index.js,code/npm/@ali/alimod-tfx-item-market-search/1.14.1/index-pc.js,code/npm/@ali/pcom-mtop/3.1.2/index.js,code/npm/@ali/pcom-dynamic/2.1.2/index.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-5481c64c.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-b67756ef.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-6a548963.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-ce9bf452.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-e1d0288e.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/addons/onDynamicDataSource/clientRequest.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/chunk-4f7c0bfa.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/addons/onDynamicDataSource/utils.js,code/npm/@ali/multimod-ice-solution-tubes__gateway-fetcher/0.0.2/addons/onDynamicDataSource/index.js,code/npm/@ali/multimod-ice-solution-tubes__page-theme/0.0.2/index.js,code/npm/@ali/multimod-ice-solution-tubes__floor-title/0.0.2/index.js,code/npm/@ali/isse-tfx-solution-plugin/1.7.0/chunk-f151e2b5.js (146)
[26404:25964:0802/124753.103:INFO:CONSOLE:3] "Error", source: https://g.alicdn.com/AWSC/et/1.83.35/et_f.js (3)
[26404:25964:0802/124753.206:INFO:CONSOLE:3] "Error", source: https://g.alicdn.com/AWSC/et/1.83.35/et_f.js (3)
[26404:25964:0802/124753.235:INFO:CONSOLE:3] "Error", source: https://g.alicdn.com/AWSC/et/1.83.35/et_f.js (3)
